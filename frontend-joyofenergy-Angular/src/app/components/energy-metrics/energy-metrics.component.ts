import { Component, Input, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { Data } from 'src/app/shared/models/dataModel';
import { EnergyMetrics } from 'src/app/shared/models/energy-metrics.model';
import { EnergyMetricsService } from 'src/app/shared/services/energy-metrics.service';

@Component({
  selector: 'app-energy-metrics',
  templateUrl: './energy-metrics.component.html',
  styleUrls: ['./energy-metrics.component.scss']
})
export class EnergyMetricsComponent implements OnInit, OnChanges {
  @Input() chartData: Data[] = [];
  
  metrics: EnergyMetrics = {
    cost: 0,
    consumption: 0,
    footprint: 0
  };

  constructor(private energyMetricsService: EnergyMetricsService) { }

  ngOnInit(): void {
    this.calculateMetrics();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['chartData'] && changes['chartData'].currentValue) {
      this.calculateMetrics();
    }
  }

  private calculateMetrics(): void {
    if (this.chartData && this.chartData.length > 0) {
      this.metrics = this.energyMetricsService.calculateMetrics(this.chartData);
    }
  }

  get formattedCost(): string {
    return this.energyMetricsService.formatCost(this.metrics.cost);
  }

  get formattedConsumption(): string {
    return this.energyMetricsService.formatConsumption(this.metrics.consumption);
  }

  get formattedFootprint(): string {
    return this.energyMetricsService.formatFootprint(this.metrics.footprint);
  }
}
