<section class="flex justify-between mb3">
  <!-- Cost Card -->
  <div class="metric-card shadow-2 roundedMore bg-white p3 flex-auto mr2">
    <div class="flex items-center mb2">
      <span class="metric-icon mr2">💰</span>
      <h3 class="h4 darkgray mb0">Cost</h3>
    </div>
    <div class="metric-value h2 bold darkgray">{{ formattedCost }}</div>
  </div>

  <!-- Consumption Card -->
  <div class="metric-card shadow-2 roundedMore bg-white p3 flex-auto mx1">
    <div class="flex items-center mb2">
      <span class="metric-icon mr2">⚡</span>
      <h3 class="h4 darkgray mb0">Consumption</h3>
    </div>
    <div class="metric-value h2 bold darkgray">{{ formattedConsumption }}</div>
  </div>

  <!-- Footprint Card -->
  <div class="metric-card shadow-2 roundedMore bg-white p3 flex-auto ml2">
    <div class="flex items-center mb2">
      <span class="metric-icon mr2">👟</span>
      <h3 class="h4 darkgray mb0">Footprint</h3>
    </div>
    <div class="metric-value h2 bold darkgray">{{ formattedFootprint }}</div>
  </div>
</section>
