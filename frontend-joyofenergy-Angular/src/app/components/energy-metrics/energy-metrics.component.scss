.metric-card {
  min-height: 120px;
  transition: transform 0.2s ease-in-out;
  border: 1px solid rgb(225, 225, 225);
  
  &:hover {
    transform: translateY(-2px);
  }
}

.metric-icon {
  font-size: 1.5rem;
  display: inline-block;
}

.metric-value {
  line-height: 1.2;
  color: rgb(109, 109, 109);
}

// Responsive design for smaller screens
@media (max-width: 768px) {
  .metric-card {
    min-height: 100px;
    
    .metric-value {
      font-size: 1.25rem;
    }
  }
  
  .metric-icon {
    font-size: 1.25rem;
  }
}
