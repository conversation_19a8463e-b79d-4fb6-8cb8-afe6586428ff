import { ComponentFixture, TestBed } from '@angular/core/testing';
import { EnergyMetricsComponent } from './energy-metrics.component';
import { EnergyMetricsService } from 'src/app/shared/services/energy-metrics.service';
import { Data } from 'src/app/shared/models/dataModel';

describe('EnergyMetricsComponent', () => {
  let component: EnergyMetricsComponent;
  let fixture: ComponentFixture<EnergyMetricsComponent>;
  let energyMetricsService: jasmine.SpyObj<EnergyMetricsService>;

  beforeEach(async () => {
    const spy = jasmine.createSpyObj('EnergyMetricsService', [
      'calculateMetrics',
      'formatCost',
      'formatConsumption',
      'formatFootprint'
    ]);

    await TestBed.configureTestingModule({
      declarations: [EnergyMetricsComponent],
      providers: [
        { provide: EnergyMetricsService, useValue: spy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(EnergyMetricsComponent);
    component = fixture.componentInstance;
    energyMetricsService = TestBed.inject(EnergyMetricsService) as jasmine.SpyObj<EnergyMetricsService>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should calculate metrics on init', () => {
    const mockData: Data[] = [
      { time: Date.now(), value: 10 },
      { time: Date.now(), value: 20 }
    ];
    const mockMetrics = { cost: 4, consumption: 30, footprint: 0.0076 };

    component.chartData = mockData;
    energyMetricsService.calculateMetrics.and.returnValue(mockMetrics);

    component.ngOnInit();

    expect(energyMetricsService.calculateMetrics).toHaveBeenCalledWith(mockData);
    expect(component.metrics).toEqual(mockMetrics);
  });

  it('should recalculate metrics when chartData changes', () => {
    const mockData: Data[] = [{ time: Date.now(), value: 15 }];
    const mockMetrics = { cost: 2, consumption: 15, footprint: 0.0038 };

    energyMetricsService.calculateMetrics.and.returnValue(mockMetrics);

    component.chartData = mockData;
    component.ngOnChanges({
      chartData: {
        currentValue: mockData,
        previousValue: [],
        firstChange: false,
        isFirstChange: () => false
      }
    });

    expect(energyMetricsService.calculateMetrics).toHaveBeenCalledWith(mockData);
    expect(component.metrics).toEqual(mockMetrics);
  });

  it('should format values correctly', () => {
    energyMetricsService.formatCost.and.returnValue('$123');
    energyMetricsService.formatConsumption.and.returnValue('456 kWh');
    energyMetricsService.formatFootprint.and.returnValue('0.1234 tonnes');

    component.metrics = { cost: 123, consumption: 456, footprint: 0.1234 };

    expect(component.formattedCost).toBe('$123');
    expect(component.formattedConsumption).toBe('456 kWh');
    expect(component.formattedFootprint).toBe('0.1234 tonnes');
  });

  it('should handle empty chart data', () => {
    const mockMetrics = { cost: 0, consumption: 0, footprint: 0 };
    energyMetricsService.calculateMetrics.and.returnValue(mockMetrics);

    component.chartData = [];
    component.ngOnInit();

    expect(component.metrics).toEqual(mockMetrics);
  });
});
