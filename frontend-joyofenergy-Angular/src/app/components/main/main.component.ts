import { Component, OnInit } from "@angular/core";
import { Data } from "src/app/shared/models/dataModel";
import { ApiService } from "src/app/shared/services/api.service";
import { renderChart } from "../../shared/utils/chart";
import {
  groupByDay,
  sortByTime,
  getReadings,
} from "../../shared/utils/reading";

@Component({
  selector: "app-main",
  templateUrl: "./main.component.html",
  styleUrls: ["./main.component.scss"],
})
export class MainComponent implements OnInit {
  chartData: Data[] = [];
  last30DaysData: Data[] = [];

  constructor() {
    this.createChart();
  }

  ngOnInit(): void {}

  async createChart() {
    const readings = await getReadings();
    const containerId = "chart";
    this.chartData = readings;

    // Process data for last 30 days
    const groupedData = sortByTime(groupByDay(readings)).slice(-30);
    this.last30DaysData = groupedData;

    renderChart(containerId, groupedData);
  }
}
