export interface EnergyMetrics {
  cost: number;           // Cost in $ rounded to nearest integer
  consumption: number;    // Consumption in kWh rounded to nearest integer  
  footprint: number;      // Carbon footprint in tonnes to 4 decimal places
}

export interface EnergyConstants {
  COST_PER_KWH: number;           // $0.138 per 1 kWh
  FOOTPRINT_PER_KWH: number;      // 0.0002532 tonnes per 1kWh
}
