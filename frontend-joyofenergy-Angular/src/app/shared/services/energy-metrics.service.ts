import { Injectable } from '@angular/core';
import { Data } from '../models/dataModel';
import { EnergyMetrics, EnergyConstants } from '../models/energy-metrics.model';

@Injectable({
  providedIn: 'root'
})
export class EnergyMetricsService implements EnergyConstants {
  // Constants as per requirements
  readonly COST_PER_KWH = 0.138;        // $0.138 per 1 kWh
  readonly FOOTPRINT_PER_KWH = 0.0002532; // 0.0002532 tonnes per 1kWh

  constructor() { }

  /**
   * Calculate energy metrics from the last 30 days of readings
   * @param readings Array of energy readings (Data[])
   * @returns EnergyMetrics object with cost, consumption, and footprint
   */
  calculateMetrics(readings: Data[]): EnergyMetrics {
    // Calculate total consumption (sum of all readings for last 30 days)
    const totalConsumption = readings.reduce((sum, reading) => sum + reading.value, 0);
    
    // Calculate cost: consumption * cost per kWh, rounded to nearest integer
    const cost = Math.round(totalConsumption * this.COST_PER_KWH);
    
    // Round consumption to nearest integer
    const consumption = Math.round(totalConsumption);
    
    // Calculate carbon footprint: consumption * footprint per kWh, to 4 decimal places
    const footprint = parseFloat((totalConsumption * this.FOOTPRINT_PER_KWH).toFixed(4));

    return {
      cost,
      consumption,
      footprint
    };
  }

  /**
   * Format cost value with currency symbol
   * @param cost Cost value
   * @returns Formatted cost string
   */
  formatCost(cost: number): string {
    return `$${cost}`;
  }

  /**
   * Format consumption value with unit
   * @param consumption Consumption value
   * @returns Formatted consumption string
   */
  formatConsumption(consumption: number): string {
    return `${consumption} kWh`;
  }

  /**
   * Format footprint value with unit
   * @param footprint Footprint value
   * @returns Formatted footprint string
   */
  formatFootprint(footprint: number): string {
    return `${footprint} tonnes`;
  }
}
