import { TestBed } from '@angular/core/testing';
import { EnergyMetricsService } from './energy-metrics.service';
import { Data } from '../models/dataModel';

describe('EnergyMetricsService', () => {
  let service: EnergyMetricsService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(EnergyMetricsService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('calculateMetrics', () => {
    it('should calculate correct metrics for sample data', () => {
      const sampleData: Data[] = [
        { time: Date.now(), value: 10 },
        { time: Date.now(), value: 20 },
        { time: Date.now(), value: 30 }
      ];

      const result = service.calculateMetrics(sampleData);

      // Total consumption: 10 + 20 + 30 = 60 kWh
      expect(result.consumption).toBe(60);
      
      // Cost: 60 * 0.138 = 8.28, rounded = 8
      expect(result.cost).toBe(8);
      
      // Footprint: 60 * 0.0002532 = 0.015192, to 4 decimal places = 0.0152
      expect(result.footprint).toBe(0.0152);
    });

    it('should handle empty data array', () => {
      const result = service.calculateMetrics([]);

      expect(result.consumption).toBe(0);
      expect(result.cost).toBe(0);
      expect(result.footprint).toBe(0);
    });

    it('should round cost to nearest integer', () => {
      const sampleData: Data[] = [
        { time: Date.now(), value: 3.6 } // 3.6 * 0.138 = 0.4968, rounds to 0
      ];

      const result = service.calculateMetrics(sampleData);
      expect(result.cost).toBe(0);
    });

    it('should round consumption to nearest integer', () => {
      const sampleData: Data[] = [
        { time: Date.now(), value: 3.7 } // rounds to 4
      ];

      const result = service.calculateMetrics(sampleData);
      expect(result.consumption).toBe(4);
    });

    it('should format footprint to 4 decimal places', () => {
      const sampleData: Data[] = [
        { time: Date.now(), value: 1000 } // 1000 * 0.0002532 = 0.2532
      ];

      const result = service.calculateMetrics(sampleData);
      expect(result.footprint).toBe(0.2532);
    });
  });

  describe('formatting methods', () => {
    it('should format cost with currency symbol', () => {
      expect(service.formatCost(123)).toBe('$123');
    });

    it('should format consumption with unit', () => {
      expect(service.formatConsumption(456)).toBe('456 kWh');
    });

    it('should format footprint with unit', () => {
      expect(service.formatFootprint(0.1234)).toBe('0.1234 tonnes');
    });
  });
});
